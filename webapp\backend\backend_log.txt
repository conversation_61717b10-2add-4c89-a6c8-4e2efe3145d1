INFO:backend.database:Tentativo di connessione al database: postgresql://postgres:Taranto@localhost:5432/cantieri
INFO:backend.database:Connessione al database riuscita
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pydantic\_internal\_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'orm_mode' has been renamed to 'from_attributes'
  warnings.warn(message, UserWarning)
INFO:     Started server process [18684]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8002 (Press CTRL+C to quit)
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Sessione del database chiusa
Stringa di connessione al database: postgresql://postgres:Taranto@localhost:5432/cantieri
Directory temporanea creata: C:\CMS\webapp\static\temp
Email service non disponibile - modalita testing attiva
Audit logger non disponibile - modalita testing attiva
Configurazione CORS con origini: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8080', 'http://localhost', 'http://localhost:5500', 'http://127.0.0.1:5500', '*']
2025-07-06 23:29:07,962 INFO sqlalchemy.engine.Engine select pg_catalog.version()
INFO:sqlalchemy.engine.Engine:select pg_catalog.version()
2025-07-06 23:29:07,962 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-07-06 23:29:07,963 INFO sqlalchemy.engine.Engine select current_schema()
INFO:sqlalchemy.engine.Engine:select current_schema()
2025-07-06 23:29:07,963 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-07-06 23:29:07,963 INFO sqlalchemy.engine.Engine show standard_conforming_strings
INFO:sqlalchemy.engine.Engine:show standard_conforming_strings
2025-07-06 23:29:07,963 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-07-06 23:29:07,964 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:29:07,964 INFO sqlalchemy.engine.Engine SELECT 1
INFO:sqlalchemy.engine.Engine:SELECT 1
2025-07-06 23:29:07,964 INFO sqlalchemy.engine.Engine [generated in 0.00010s] {}
INFO:sqlalchemy.engine.Engine:[generated in 0.00010s] {}
INFO:     127.0.0.1:53355 - "GET /api/health HTTP/1.1" 200 OK
INFO:     127.0.0.1:53398 - "OPTIONS /api/auth/test-token HTTP/1.1" 200 OK
INFO:     127.0.0.1:53399 - "OPTIONS /api/auth/test-token HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:29:34,500 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:29:34,505 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:29:34,505 INFO sqlalchemy.engine.Engine [generated in 0.00041s] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00041s] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Test token riuscito per utente: a, ruolo: user, impersonato: False
2025-07-06 23:29:34,510 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53398 - "POST /api/auth/test-token HTTP/1.1" 200 OK
2025-07-06 23:29:34,547 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:29:34,547 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:29:34,548 INFO sqlalchemy.engine.Engine [cached since 0.04282s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.04282s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Test token riuscito per utente: a, ruolo: user, impersonato: False
2025-07-06 23:29:34,552 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53399 - "POST /api/auth/test-token HTTP/1.1" 200 OK
INFO:     127.0.0.1:53399 - "OPTIONS /api/cantieri HTTP/1.1" 200 OK
INFO:     127.0.0.1:53398 - "OPTIONS /api/cantieri HTTP/1.1" 200 OK
INFO:     127.0.0.1:53399 - "GET /api/cantieri HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:53399 - "GET /api/cantieri HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:53399 - "OPTIONS /api/cantieri/ HTTP/1.1" 200 OK
INFO:     127.0.0.1:53398 - "OPTIONS /api/cantieri/ HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:29:35,122 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:29:35,122 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:29:35,122 INFO sqlalchemy.engine.Engine [cached since 0.6174s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.6174s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 23:29:35,125 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
2025-07-06 23:29:35,126 INFO sqlalchemy.engine.Engine [generated in 0.00016s] {'id_utente_1': 2}
INFO:sqlalchemy.engine.Engine:[generated in 0.00016s] {'id_utente_1': 2}
INFO:backend.database:Sessione del database chiusa
2025-07-06 23:29:35,129 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53399 - "GET /api/cantieri/ HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:29:35,132 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:29:35,133 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:29:35,133 INFO sqlalchemy.engine.Engine [cached since 0.6279s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 0.6279s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 23:29:35,134 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
2025-07-06 23:29:35,134 INFO sqlalchemy.engine.Engine [cached since 0.008969s ago] {'id_utente_1': 2}
INFO:sqlalchemy.engine.Engine:[cached since 0.008969s ago] {'id_utente_1': 2}
INFO:backend.database:Sessione del database chiusa
2025-07-06 23:29:35,138 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53399 - "GET /api/cantieri/ HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:29:57,391 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:29:57,391 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:29:57,392 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:29:57,392 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:29:57,392 INFO sqlalchemy.engine.Engine [cached since 22.89s ago] {'id_utente_1': 2, 'param_1': 1}
2025-07-06 23:29:57,392 INFO sqlalchemy.engine.Engine [cached since 22.89s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 22.89s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 22.89s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Utente a autenticato con successo
Test token riuscito per utente: a, ruolo: user, impersonato: False
Test token riuscito per utente: a, ruolo: user, impersonato: False
2025-07-06 23:29:57,394 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:backend.database:Sessione del database chiusa
INFO:sqlalchemy.engine.Engine:ROLLBACK
2025-07-06 23:29:57,394 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53406 - "POST /api/auth/test-token HTTP/1.1" 200 OK
INFO:     127.0.0.1:53405 - "POST /api/auth/test-token HTTP/1.1" 200 OK
INFO:     127.0.0.1:53405 - "GET /api/cantieri HTTP/1.1" 307 Temporary Redirect
INFO:     127.0.0.1:53405 - "GET /api/cantieri HTTP/1.1" 307 Temporary Redirect
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:29:57,985 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:29:57,985 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:29:57,985 INFO sqlalchemy.engine.Engine [cached since 23.48s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 23.48s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 23:29:57,987 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
2025-07-06 23:29:57,987 INFO sqlalchemy.engine.Engine [cached since 22.86s ago] {'id_utente_1': 2}
INFO:sqlalchemy.engine.Engine:[cached since 22.86s ago] {'id_utente_1': 2}
INFO:backend.database:Sessione del database chiusa
2025-07-06 23:29:57,988 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53405 - "GET /api/cantieri/ HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:29:57,991 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:29:57,991 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:29:57,992 INFO sqlalchemy.engine.Engine [cached since 23.49s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 23.49s ago] {'id_utente_1': 2, 'param_1': 1}
Utente a autenticato con successo
2025-07-06 23:29:57,993 INFO sqlalchemy.engine.Engine SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
INFO:sqlalchemy.engine.Engine:SELECT cantieri.id_cantiere AS cantieri_id_cantiere, cantieri.commessa AS cantieri_commessa, cantieri.descrizione AS cantieri_descrizione, cantieri.nome_cliente AS cantieri_nome_cliente, cantieri.indirizzo_cantiere AS cantieri_indirizzo_cantiere, cantieri.citta_cantiere AS cantieri_citta_cantiere, cantieri.nazione_cantiere AS cantieri_nazione_cantiere, cantieri.data_creazione AS cantieri_data_creazione, cantieri.password_cantiere AS cantieri_password_cantiere, cantieri.password_cantiere_encrypted AS cantieri_password_cantiere_encrypted, cantieri.id_utente AS cantieri_id_utente, cantieri.codice_univoco AS cantieri_codice_univoco, cantieri.riferimenti_normativi AS cantieri_riferimenti_normativi, cantieri.documentazione_progetto AS cantieri_documentazione_progetto, cantieri.reset_token AS cantieri_reset_token, cantieri.reset_token_expires AS cantieri_reset_token_expires, cantieri.reset_token_used AS cantieri_reset_token_used, cantieri.failed_login_attempts AS cantieri_failed_login_attempts, cantieri.locked_until AS cantieri_locked_until, cantieri.last_login AS cantieri_last_login, cantieri.last_password_change AS cantieri_last_password_change, cantieri.updated_at AS cantieri_updated_at, cantieri.last_activity AS cantieri_last_activity 
FROM cantieri 
WHERE cantieri.id_utente = %(id_utente_1)s
2025-07-06 23:29:57,993 INFO sqlalchemy.engine.Engine [cached since 22.87s ago] {'id_utente_1': 2}
INFO:sqlalchemy.engine.Engine:[cached since 22.87s ago] {'id_utente_1': 2}
INFO:backend.database:Sessione del database chiusa
2025-07-06 23:29:57,994 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53405 - "GET /api/cantieri/ HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:31:37,989 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:31:37,989 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:31:37,989 INFO sqlalchemy.engine.Engine [cached since 123.5s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 123.5s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Test token riuscito per utente: a, ruolo: user, impersonato: False
2025-07-06 23:31:37,991 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53443 - "POST /api/auth/test-token HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:33:12,807 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:33:12,807 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:33:12,807 INFO sqlalchemy.engine.Engine [cached since 218.3s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 218.3s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Test token riuscito per utente: a, ruolo: user, impersonato: False
2025-07-06 23:33:12,809 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53464 - "POST /api/auth/test-token HTTP/1.1" 200 OK
INFO:backend.database:Nuova sessione del database creata
Tentativo di decodifica token: eyJhbGciOiJIUzI1NiIs...
Token decodificato con successo: {'sub': 'a', 'user_id': 2, 'role': 'user', 'exp': **********}
Dati estratti dal token - username: a, user_id: 2, role: user, is_impersonated: False
Ricerca utente nel database con id: 2
2025-07-06 23:33:12,855 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-07-06 23:33:12,855 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare, utenti.reset_token AS utenti_reset_token, utenti.reset_token_expires AS utenti_reset_token_expires, utenti.reset_token_used AS utenti_reset_token_used, utenti.failed_login_attempts AS utenti_failed_login_attempts, utenti.locked_until AS utenti_locked_until, utenti.last_login AS utenti_last_login, utenti.last_password_change AS utenti_last_password_change, utenti.created_at AS utenti_created_at, utenti.updated_at AS utenti_updated_at, utenti.last_activity AS utenti_last_activity 
FROM utenti 
WHERE utenti.id_utente = %(id_utente_1)s 
 LIMIT %(param_1)s
2025-07-06 23:33:12,855 INFO sqlalchemy.engine.Engine [cached since 218.4s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 218.4s ago] {'id_utente_1': 2, 'param_1': 1}
INFO:backend.database:Sessione del database chiusa
Utente a autenticato con successo
Test token riuscito per utente: a, ruolo: user, impersonato: False
2025-07-06 23:33:12,857 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53464 - "POST /api/auth/test-token HTTP/1.1" 200 OK
