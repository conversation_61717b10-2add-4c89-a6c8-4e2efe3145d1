'use client'

import { useState, useEffect, useRef } from 'react'
import * as VisuallyHidden from '@radix-ui/react-visually-hidden'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, Zap, CheckCircle, AlertTriangle, X } from 'lucide-react'
import { Cavo } from '@/types'
import { caviApi } from '@/lib/api'
import { useAuth } from '@/contexts/AuthContext'
import { useToastActions } from '@/hooks/use-toast'

interface CollegamentiDialogProps {
  open: boolean
  onClose: () => void
  cavo: Cavo | null
  onSuccess?: () => void
  onError?: (error: string) => void
}

interface ConfirmDialogState {
  open: boolean
  type: 'partenza' | 'arrivo' | 'entrambi' | null
  title: string
  description: string
}

interface ConfirmDisconnectDialogProps {
  open: boolean
  onClose: () => void
  onConfirm: () => void
  title: string
  description: string
  isLoading: boolean
  isDangerous?: boolean
}

function ConfirmDisconnectDialog({
  open,
  onClose,
  onConfirm,
  title,
  description,
  isLoading,
  isDangerous = false
}: ConfirmDisconnectDialogProps) {
  const [showFinalConfirmation, setShowFinalConfirmation] = useState(false)

  useEffect(() => {
    if (!open) {
      setShowFinalConfirmation(false)
    }
  }, [open])

  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open && !isLoading) {
        onClose()
      }
    }

    if (open) {
      document.addEventListener('keydown', handleEsc)
      return () => document.removeEventListener('keydown', handleEsc)
    }
  }, [open, onClose, isLoading])

  const handleInitialConfirm = () => {
    if (isDangerous) {
      setShowFinalConfirmation(true)
    } else {
      onConfirm()
    }
  }

  const handleFinalConfirm = () => {
    onConfirm()
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-[400px]"
        aria-describedby="confirm-disconnect-description"
      >
        {!showFinalConfirmation ? (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2 text-orange-600">
                <AlertTriangle className="h-5 w-5" />
                {title}
              </DialogTitle>
              <DialogDescription id="confirm-disconnect-description">
                {description}
              </DialogDescription>
            </DialogHeader>

            <div className="py-4">
              <Alert className="border-orange-200 bg-orange-50">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  <strong>Attenzione:</strong> Questa azione modificherà lo stato del collegamento del cavo.
                </AlertDescription>
              </Alert>
            </div>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
                className="flex-1 hover:bg-gray-50"
              >
                Annulla
              </Button>
              <Button
                variant="outline"
                onClick={handleInitialConfirm}
                disabled={isLoading}
                className="flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
              >
                <AlertTriangle className="mr-2 h-4 w-4" />
                {isDangerous ? 'Procedi' : 'Conferma'}
              </Button>
            </DialogFooter>
          </>
        ) : (
          <>
            <DialogHeader>
              <DialogTitle className="text-center text-orange-600">
                Conferma Finale
              </DialogTitle>
            </DialogHeader>

            <div className="py-4 text-center">
              <div className="mx-auto w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Sei veramente sicuro?
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Questa azione scollegherà <strong>entrambi i lati</strong> del cavo.
              </p>
              <div className="bg-orange-50 border border-orange-200 rounded-md p-3">
                <p className="text-sm text-orange-800 font-medium">
                  ⚠️ Operazione irreversibile
                </p>
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button
                variant="outline"
                onClick={() => setShowFinalConfirmation(false)}
                disabled={isLoading}
                className="flex-1"
              >
                No, Annulla
              </Button>
              <Button
                variant="outline"
                onClick={handleFinalConfirm}
                disabled={isLoading}
                className="flex-1 bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 hover:border-orange-300"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Scollegando...
                  </>
                ) : (
                  'Sì, Scollega'
                )}
              </Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  )
}

function CollegamentiDialog({
  open,
  onClose,
  cavo,
  onSuccess,
  onError
}: CollegamentiDialogProps) {
  const { cantiere } = useAuth()
  const toast = useToastActions()

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogState>({
    open: false,
    type: null,
    title: '',
    description: ''
  })

  const dialogRef = useRef<HTMLDivElement>(null)
  const firstFocusableRef = useRef<HTMLButtonElement>(null)
  const lastFocusableRef = useRef<HTMLButtonElement>(null)

  const [screenReaderAnnouncement, setScreenReaderAnnouncement] = useState('')

  useEffect(() => {
    if (open && cavo) {
      setError('')
    }
  }, [open, cavo])

  useEffect(() => {
    if (open && dialogRef.current) {
      const focusableElements = dialogRef.current.querySelectorAll(
        'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
      )

      if (focusableElements.length > 0) {
        (focusableElements[0] as HTMLElement).focus()
      }
    }
  }, [open])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!open || loading || confirmDialog.open) return

      switch (e.key) {
        case 'Escape':
          onClose()
          break

        case '1':
          e.preventDefault()
          handleCollegaPartenza()
          break

        case '2':
          e.preventDefault()
          handleCollegaArrivo()
          break

        case '3':
          e.preventDefault()
          handleCollegaEntrambi()
          break

        case 'Tab':
          const focusableElements = dialogRef.current?.querySelectorAll(
            'button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'
          )

          const firstElement = focusableElements?.[0] as HTMLElement
          const lastElement = focusableElements?.[focusableElements.length - 1] as HTMLElement

          if (e.shiftKey) {
            if (document.activeElement === firstElement) {
              e.preventDefault()
              lastElement?.focus()
            }
          } else {
            if (document.activeElement === lastElement) {
              e.preventDefault()
              firstElement?.focus()
            }
          }
          break
      }
    }

    if (open) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [open, onClose, loading, confirmDialog.open])



  const getStatoCollegamento = () => {
    if (!cavo) return {
      stato: 'non_collegato',
      descrizione: 'Non collegato',
      dettaglio: 'Nessun lato collegato',
      progressoPercentuale: 0,
      colore: 'gray'
    }

    const collegamento = cavo.collegamento || cavo.collegamenti || 0

    switch (collegamento) {
      case 1:
        return {
          stato: 'partenza',
          descrizione: '🟢⚪ Collegato lato partenza',
          dettaglio: 'Collegamento parziale - manca lato arrivo',
          progressoPercentuale: 50,
          colore: 'amber'
        }
      case 2:
        return {
          stato: 'arrivo',
          descrizione: '⚪🟢 Collegato lato arrivo',
          dettaglio: 'Collegamento parziale - manca lato partenza',
          progressoPercentuale: 50,
          colore: 'amber'
        }
      case 3:
        return {
          stato: 'completo',
          descrizione: '🟢🟢 Completamente collegato',
          dettaglio: 'Entrambi i lati collegati correttamente',
          progressoPercentuale: 100,
          colore: 'green'
        }
      default:
        return {
          stato: 'non_collegato',
          descrizione: '⚪⚪ Non collegato',
          dettaglio: 'Nessun lato collegato',
          progressoPercentuale: 0,
          colore: 'gray'
        }
    }
  }

  const handleCollegaPartenza = () => {
    if (!cavo) return

    const statoAttuale = getStatoCollegamento()

    if (statoAttuale.stato === 'partenza' || statoAttuale.stato === 'completo') {
      setConfirmDialog({
        open: true,
        type: 'partenza',
        title: 'Scollega lato partenza',
        description: `Vuoi scollegare il lato partenza del cavo ${cavo.id_cavo}?`
      })
    } else {
      executeCollegaPartenza()
    }
  }

  const executeCollegaPartenza = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Collegamento lato partenza in corso...')

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'partenza',
        'cantiere'
      )

      const message = `Collegamento lato partenza completato per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast.success("Successo", message)

      // Aggiorna lo stato locale del cavo senza ricaricare la pagina
      if (cavo) {
        cavo.collegamenti = (cavo.collegamenti || 0) | 1 // Imposta bit partenza
      }

      if (onSuccess) onSuccess()
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleCollegaArrivo = () => {
    if (!cavo) return

    const statoAttuale = getStatoCollegamento()

    if (statoAttuale.stato === 'arrivo' || statoAttuale.stato === 'completo') {
      setConfirmDialog({
        open: true,
        type: 'arrivo',
        title: 'Scollega lato arrivo',
        description: `Vuoi scollegare il lato arrivo del cavo ${cavo.id_cavo}?`
      })
    } else {
      executeCollegaArrivo()
    }
  }

  const executeCollegaArrivo = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Collegamento lato arrivo in corso...')

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'arrivo',
        'cantiere'
      )

      const message = `Collegamento lato arrivo completato per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast.success("Successo", message)

      // Aggiorna lo stato locale del cavo senza ricaricare la pagina
      if (cavo) {
        cavo.collegamenti = (cavo.collegamenti || 0) | 2 // Imposta bit arrivo
      }

      if (onSuccess) onSuccess()
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const handleCollegaEntrambi = () => {
    if (!cavo) return

    const statoAttuale = getStatoCollegamento()

    if (statoAttuale.stato === 'completo') {
      setConfirmDialog({
        open: true,
        type: 'entrambi',
        title: 'Scollega entrambi i lati',
        description: `Vuoi scollegare completamente il cavo ${cavo.id_cavo}? Questa operazione rimuoverà tutti i collegamenti.`
      })
    } else {
      executeCollegaEntrambi()
    }
  }

  const executeCollegaEntrambi = async () => {
    if (!cavo || !cantiere) return

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Collegamento entrambi i lati in corso...')

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'partenza',
        'cantiere'
      )

      await caviApi.collegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        'arrivo',
        'cantiere'
      )

      const message = `Collegamento completo per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast.success("Successo", message)

      // Aggiorna lo stato locale del cavo senza ricaricare la pagina
      if (cavo) {
        cavo.collegamenti = 3 // Entrambi i lati collegati
      }

      if (onSuccess) onSuccess()
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante il collegamento'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  const executeDisconnect = async () => {
    if (!cavo || !cantiere || !confirmDialog.type) return

    try {
      setLoading(true)
      setError('')
      announceToScreenReader('Scollegamento in corso...')

      await caviApi.scollegaCavo(
        cantiere.id_cantiere,
        cavo.id_cavo,
        confirmDialog.type === 'entrambi' ? undefined : confirmDialog.type
      )

      const latoText = confirmDialog.type === 'entrambi' ? '' : ` lato ${confirmDialog.type}`
      const message = `Scollegamento${latoText} completato per il cavo ${cavo.id_cavo}`
      announceToScreenReader(message)
      toast.success("Successo", message)

      // Aggiorna lo stato locale del cavo senza ricaricare la pagina
      if (cavo) {
        if (confirmDialog.type === 'entrambi') {
          cavo.collegamenti = 0 // Nessun collegamento
        } else if (confirmDialog.type === 'partenza') {
          cavo.collegamenti = (cavo.collegamenti || 0) & ~1 // Rimuovi bit partenza
        } else if (confirmDialog.type === 'arrivo') {
          cavo.collegamenti = (cavo.collegamenti || 0) & ~2 // Rimuovi bit arrivo
        }
      }

      if (onSuccess) onSuccess()
      setConfirmDialog({ open: false, type: null, title: '', description: '' })
      onClose()
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || error.message || 'Errore durante lo scollegamento'
      setError(errorMessage)
      announceToScreenReader(`Errore: ${errorMessage}`)
      if (onError) onError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Funzioni di supporto per accessibilità
  const announceToScreenReader = (message: string) => {
    setScreenReaderAnnouncement(message)
    setTimeout(() => setScreenReaderAnnouncement(''), 1000)
  }

  if (!cavo) return null

  const statoCollegamento = getStatoCollegamento()
  // Verifica se il cavo è installato (più permissiva)
  const isInstalled = cavo.stato_installazione?.toLowerCase() === 'installato' ||
                     (cavo.metri_posati || cavo.metratura_reale || 0) > 0

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        ref={dialogRef}
        className="sm:max-w-[500px]"
        aria-describedby="collegamenti-dialog-description"
        role="dialog"
        aria-labelledby="collegamenti-dialog-title"
        aria-modal="true"
      >
        <VisuallyHidden.Root>
          <DialogTitle id="collegamenti-dialog-title">
            Gestione Collegamenti - Cavo {cavo.id_cavo}
          </DialogTitle>
        </VisuallyHidden.Root>

        <DialogHeader>
          <div className="flex items-center gap-2 text-lg font-semibold">
            <Zap className="h-5 w-5 text-blue-600" />
            Gestione Collegamenti - Cavo {cavo.id_cavo}
          </div>
          <DialogDescription id="collegamenti-dialog-description">
            Gestisci i collegamenti elettrici del cavo selezionato
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6" role="main">
          <div
            aria-live="polite"
            aria-atomic="true"
            className="sr-only"
          >
            {screenReaderAnnouncement}
          </div>

          <section className="p-4 bg-gray-50 rounded-lg space-y-3"
            aria-labelledby="stato-collegamenti" role="region">
            <h3 id="stato-collegamenti" className="text-sm font-medium">
              Stato Attuale Collegamenti
            </h3>

            <div
              className="mt-1 text-lg font-semibold"
              aria-live="polite"
              aria-describedby="stato-collegamenti"
            >
              {statoCollegamento.descrizione}
            </div>

            <div className="mt-2 space-y-2">
              <div className="text-sm text-gray-600">
                {statoCollegamento.dettaglio}
              </div>

              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    statoCollegamento.colore === 'green' ? 'bg-green-500' :
                    statoCollegamento.colore === 'amber' ? 'bg-amber-500' :
                    'bg-gray-400'
                  }`}
                  style={{ width: `${statoCollegamento.progressoPercentuale}%` }}
                  role="progressbar"
                  aria-valuenow={statoCollegamento.progressoPercentuale}
                  aria-valuemin={0}
                  aria-valuemax={100}
                  aria-label={`Progresso collegamento: ${statoCollegamento.progressoPercentuale}%`}
                />
              </div>

              <div className="text-xs text-gray-500 text-right">
                Progresso: {statoCollegamento.progressoPercentuale}%
              </div>
            </div>

            <div
              className="grid grid-cols-2 gap-4 text-sm"
              role="group"
              aria-label="Dettagli collegamenti per lato"
            >
              <div>
                <Label className="text-xs text-gray-600" id="lato-partenza-label">
                  Lato Partenza
                </Label>
                <div className="mt-1">
                  <div className="space-y-1">
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                        (cavo.collegamenti || 0) & 1 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                      }`}
                      role="status"
                      aria-labelledby="lato-partenza-label"
                      aria-describedby={cavo.responsabile_partenza ? "resp-partenza" : undefined}
                    >
                      {(cavo.collegamenti || 0) & 1 ? '🟢 Collegato' : '⚪ Non collegato'}
                    </span>

                    {statoCollegamento.stato === 'arrivo' && (
                      <div className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                        ⚠️ Prossimo: collegare questo lato
                      </div>
                    )}
                  </div>

                  {cavo.responsabile_partenza && (
                    <div
                      id="resp-partenza"
                      className="text-xs text-gray-600 mt-1"
                      aria-label={`Responsabile lato partenza: ${cavo.responsabile_partenza}`}
                    >
                      Resp: {cavo.responsabile_partenza}
                    </div>
                  )}
                </div>
              </div>

              <div>
                <Label className="text-xs text-gray-600" id="lato-arrivo-label">
                  Lato Arrivo
                </Label>
                <div className="mt-1">
                  <div className="space-y-1">
                    <span
                      className={`inline-flex items-center px-2 py-1 rounded-full text-xs ${
                        (cavo.collegamenti || 0) & 2 ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                      }`}
                      role="status"
                      aria-labelledby="lato-arrivo-label"
                      aria-describedby={cavo.responsabile_arrivo ? "resp-arrivo" : undefined}
                    >
                      {(cavo.collegamenti || 0) & 2 ? '🟢 Collegato' : '⚪ Non collegato'}
                    </span>

                    {statoCollegamento.stato === 'partenza' && (
                      <div className="text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
                        ⚠️ Prossimo: collegare questo lato
                      </div>
                    )}
                  </div>

                  {cavo.responsabile_arrivo && (
                    <div
                      id="resp-arrivo"
                      className="text-xs text-gray-600 mt-1"
                      aria-label={`Responsabile lato arrivo: ${cavo.responsabile_arrivo}`}
                    >
                      Resp: {cavo.responsabile_arrivo}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </section>

          {!isInstalled && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <p>Il cavo deve essere installato prima di poter essere collegato.</p>
                  <div className="text-xs text-gray-600 space-y-1">
                    <p><strong>Stato attuale:</strong> {cavo.stato_installazione || 'Non definito'}</p>
                    <p><strong>Metri posati:</strong> {cavo.metri_posati || 0}</p>
                    <p><strong>Metratura reale:</strong> {cavo.metratura_reale || 0}</p>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {isInstalled && (statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'arrivo') && (
            <Alert className="border-amber-200 bg-amber-50">
              <AlertCircle className="h-4 w-4 text-amber-600" />
              <AlertDescription className="text-amber-800">
                <strong>Collegamento parziale rilevato!</strong><br />
                {statoCollegamento.stato === 'partenza'
                  ? 'Il lato partenza è collegato. Completa il collegamento collegando anche il lato arrivo.'
                  : 'Il lato arrivo è collegato. Completa il collegamento collegando anche il lato partenza.'
                }
              </AlertDescription>
            </Alert>
          )}

          {(isInstalled || process.env.NODE_ENV === 'development') && (
            <>
              {!isInstalled && (
                <Alert className="border-yellow-200 bg-yellow-50">
                  <AlertCircle className="h-4 w-4 text-yellow-600" />
                  <AlertDescription className="text-yellow-800">
                    <strong>Modalità Debug:</strong> Il cavo non è installato ma puoi testare il collegamento.
                  </AlertDescription>
                </Alert>
              )}



              <section className="space-y-4" aria-labelledby="azioni-collegamento">
                <Label className="text-sm font-medium">Azioni Disponibili</Label>

                <div className="grid grid-cols-1 gap-3">
                  <Button
                    ref={firstFocusableRef}
                    onClick={handleCollegaPartenza}
                    disabled={loading}
                    className="w-full h-12 text-left justify-start bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300"
                    variant="outline"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-bold text-green-700">1</span>
                        </div>
                        <div>
                          <div className="font-medium">
                            {statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'completo'
                              ? 'Scollega Partenza'
                              : 'Collega Partenza'}
                          </div>
                          <div className="text-xs text-green-600">
                            {statoCollegamento.stato === 'partenza' || statoCollegamento.stato === 'completo'
                              ? 'Rimuovi collegamento lato partenza'
                              : 'Connetti il lato partenza del cavo'}
                          </div>
                        </div>
                      </div>
                      {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Zap className="h-4 w-4" />}
                    </div>
                  </Button>

                  <Button
                    onClick={handleCollegaArrivo}
                    disabled={loading}
                    className="w-full h-12 text-left justify-start bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 hover:border-blue-300"
                    variant="outline"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-bold text-blue-700">2</span>
                        </div>
                        <div>
                          <div className="font-medium">
                            {statoCollegamento.stato === 'arrivo' || statoCollegamento.stato === 'completo'
                              ? 'Scollega Arrivo'
                              : 'Collega Arrivo'}
                          </div>
                          <div className="text-xs text-blue-600">
                            {statoCollegamento.stato === 'arrivo' || statoCollegamento.stato === 'completo'
                              ? 'Rimuovi collegamento lato arrivo'
                              : 'Connetti il lato arrivo del cavo'}
                          </div>
                        </div>
                      </div>
                      {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Zap className="h-4 w-4" />}
                    </div>
                  </Button>

                  <Button
                    onClick={handleCollegaEntrambi}
                    disabled={loading}
                    className="w-full h-12 text-left justify-start bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 hover:border-purple-300"
                    variant="outline"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-bold text-purple-700">3</span>
                        </div>
                        <div>
                          <div className="font-medium">
                            {statoCollegamento.stato === 'completo'
                              ? 'Scollega Completamente'
                              : 'Collega Entrambi'}
                          </div>
                          <div className="text-xs text-purple-600">
                            {statoCollegamento.stato === 'completo'
                              ? 'Rimuovi tutti i collegamenti'
                              : 'Connetti entrambi i lati del cavo'}
                          </div>
                        </div>
                      </div>
                      {loading ? <Loader2 className="h-4 w-4 animate-spin" /> : <Zap className="h-4 w-4" />}
                    </div>
                  </Button>
                </div>
              </section>
            </>
          )}
        </div>

        <DialogFooter>
          <Button
            ref={lastFocusableRef}
            variant="outline"
            onClick={onClose}
            disabled={loading}
            className="hover:bg-gray-50"
          >
            <X className="mr-2 h-4 w-4" />
            Chiudi
          </Button>
        </DialogFooter>
      </DialogContent>

      <ConfirmDisconnectDialog
        open={confirmDialog.open}
        onClose={() => setConfirmDialog({ open: false, type: null, title: '', description: '' })}
        onConfirm={executeDisconnect}
        title={confirmDialog.title}
        description={confirmDialog.description}
        isLoading={loading}
        isDangerous={confirmDialog.type === 'entrambi'}
      />
    </Dialog>
  )
}

export default CollegamentiDialog
