import { NextRequest, NextResponse } from 'next/server'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ cantiereId: string; cavoId: string }> }
) {
  try {
    const { cantiereId, cavoId } = await params
    
    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          detail: 'Token di autorizzazione mancante' 
        }, 
        { status: 401 }
      )
    }

    // Leggi il body della richiesta
    const body = await request.json().catch(() => ({}))

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    const url = `${backendUrl}/api/cavi/${cantiereId}/${cavoId}/mark-as-spare`
    
    console.log('🔄 Mark-as-Spare API: Proxying POST request to backend:', url)
    console.log('🔄 Mark-as-Spare API: Request body:', body)
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      },
      body: JSON.stringify(body)
    })

    console.log('📡 Mark-as-Spare API: Backend response status:', response.status)

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))
      console.error('❌ Mark-as-Spare API: Backend error:', errorData)
      return NextResponse.json(errorData, { 
        status: response.status 
      })
    }

    const data = await response.json()
    console.log('📡 Mark-as-Spare API: Backend response data:', data)

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    console.error('❌ Mark-as-Spare API: Errore interno:', error)
    return NextResponse.json(
      { detail: 'Errore interno del server' },
      { status: 500 }
    )
  }
}
