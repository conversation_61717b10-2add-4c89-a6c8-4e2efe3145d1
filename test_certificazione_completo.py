#!/usr/bin/env python3
"""
Test completo per verificare il sistema di certificazione cavi
in webapp React e webapp-nextjs_1, analizzando l'interazione con il database.
"""

import requests
import json
from datetime import datetime

# Configurazione
BASE_URL = "http://localhost:8001"
FRONTEND_URL = "http://localhost:3001"

def login():
    """Effettua il login e restituisce il token."""
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    response = requests.post(f"{BASE_URL}/api/auth/login", data=login_data)
    if response.status_code == 200:
        data = response.json()
        return data.get("access_token")
    else:
        print(f"❌ Errore login: {response.status_code} - {response.text}")
        return None

def get_cavi_installati(token, cantiere_id):
    """Recupera i cavi installati e collegati."""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/cavi/{cantiere_id}?stato=Installato", headers=headers)
    
    if response.status_code == 200:
        cavi = response.json()
        # Filtra solo i cavi collegati (collegamenti = 3)
        cavi_collegati = [c for c in cavi if c.get('collegamenti', 0) == 3]
        return cavi_collegati
    else:
        print(f"❌ Errore recupero cavi: {response.status_code} - {response.text}")
        return []

def get_strumenti(token, cantiere_id):
    """Recupera gli strumenti certificati."""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/cantieri/{cantiere_id}/strumenti", headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Errore recupero strumenti: {response.status_code} - {response.text}")
        return []

def get_certificazioni_esistenti(token, cantiere_id):
    """Recupera le certificazioni esistenti."""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/cantieri/{cantiere_id}/certificazioni", headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Errore recupero certificazioni: {response.status_code} - {response.text}")
        return []

def test_certificazione_backend(token, cantiere_id, cavo_id, strumento_id=None):
    """Testa la creazione certificazione direttamente al backend."""
    headers = {"Authorization": f"Bearer {token}"}
    
    # Dati certificazione di test
    data = {
        "id_cavo": cavo_id,
        "id_operatore": "test_operator",
        "strumento_utilizzato": "Multimetro digitale",
        "id_strumento": strumento_id,
        "lunghezza_misurata": 25.5,
        "valore_continuita": "OK",
        "valore_isolamento": "500",
        "valore_resistenza": "OK",
        "note": "Test certificazione automatica",
        # Campi CEI 64-8
        "tipo_certificato": "SINGOLO",
        "stato_certificato": "CONFORME",
        "tensione_nominale": "230V",
        "tensione_prova_isolamento": 500,
        "durata_prova_isolamento": 60,
        "valore_minimo_isolamento": 1.0,
        "temperatura_prova": 20.5,
        "umidita_prova": 65.0,
        "esito_complessivo": "CONFORME"
    }
    
    print(f"🔄 Test certificazione backend per cavo {cavo_id}")
    print(f"📝 Dati inviati: {json.dumps(data, indent=2)}")
    
    response = requests.post(f"{BASE_URL}/api/cantieri/{cantiere_id}/certificazioni", 
                           json=data, headers=headers)
    
    print(f"📡 Risposta backend: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Certificazione backend creata con successo")
        print(f"📊 ID Certificazione: {result.get('id_certificazione')}")
        print(f"📊 Numero Certificato: {result.get('numero_certificato')}")
        return result
    else:
        print(f"❌ Errore certificazione backend: {response.text}")
        try:
            error_data = response.json()
            print(f"📝 Dettagli errore: {error_data}")
        except:
            pass
        return None

def test_certificazione_frontend(token, cantiere_id, cavo_id, strumento_id=None):
    """Testa la creazione certificazione tramite frontend Next.js."""
    headers = {"Authorization": f"Bearer {token}"}
    
    # Dati certificazione di test
    data = {
        "id_cavo": cavo_id,
        "id_operatore": "frontend_operator",
        "strumento_utilizzato": "Tester isolamento",
        "id_strumento": strumento_id,
        "lunghezza_misurata": 30.0,
        "valore_continuita": "OK",
        "valore_isolamento": "1000",
        "valore_resistenza": "OK",
        "note": "Test certificazione frontend",
        # Campi CEI 64-8
        "tipo_certificato": "SINGOLO",
        "stato_certificato": "CONFORME",
        "tensione_nominale": "400V",
        "tensione_prova_isolamento": 1000,
        "durata_prova_isolamento": 120,
        "valore_minimo_isolamento": 2.0,
        "temperatura_prova": 22.0,
        "umidita_prova": 60.0,
        "esito_complessivo": "CONFORME"
    }
    
    print(f"🔄 Test certificazione frontend per cavo {cavo_id}")
    print(f"📝 Dati inviati: {json.dumps(data, indent=2)}")
    
    response = requests.post(f"{FRONTEND_URL}/api/cantieri/{cantiere_id}/certificazioni", 
                           json=data, headers=headers)
    
    print(f"📡 Risposta frontend: {response.status_code}")
    if response.status_code == 200:
        result = response.json()
        print(f"✅ Certificazione frontend creata con successo")
        print(f"📊 ID Certificazione: {result.get('id_certificazione')}")
        print(f"📊 Numero Certificato: {result.get('numero_certificato')}")
        return result
    else:
        print(f"❌ Errore certificazione frontend: {response.text}")
        try:
            error_data = response.json()
            print(f"📝 Dettagli errore: {error_data}")
        except:
            pass
        return None

def analizza_database_certificazioni(token, cantiere_id):
    """Analizza lo stato delle certificazioni nel database."""
    print(f"\n📊 ANALISI DATABASE CERTIFICAZIONI")
    
    certificazioni = get_certificazioni_esistenti(token, cantiere_id)
    print(f"📋 Trovate {len(certificazioni)} certificazioni nel database")
    
    for cert in certificazioni[-5:]:  # Mostra le ultime 5
        print(f"  🔖 ID: {cert.get('id_certificazione')} | Cavo: {cert.get('id_cavo')} | Numero: {cert.get('numero_certificato')} | Data: {cert.get('data_certificazione')}")

def main():
    print("🚀 Avvio test sistema certificazione completo")
    print("📋 Analisi interazione con database e confronto webapp React vs webapp-nextjs_1")
    
    # Login
    token = login()
    if not token:
        print("❌ Impossibile effettuare il login")
        return
    
    print("✅ Login riuscito, token ottenuto")
    
    # Configurazione test
    cantiere_id = 1
    
    # Analisi stato iniziale
    print(f"\n📊 ANALISI STATO INIZIALE")
    cavi = get_cavi_installati(token, cantiere_id)
    strumenti = get_strumenti(token, cantiere_id)
    certificazioni_iniziali = get_certificazioni_esistenti(token, cantiere_id)
    
    print(f"📋 Cavi installati e collegati: {len(cavi)}")
    print(f"🔧 Strumenti disponibili: {len(strumenti)}")
    print(f"📜 Certificazioni esistenti: {len(certificazioni_iniziali)}")
    
    if not cavi:
        print("❌ Nessun cavo installato e collegato trovato per il test")
        return
    
    # Seleziona cavi per il test (che non siano già certificati)
    cavi_certificati = {cert['id_cavo'] for cert in certificazioni_iniziali}
    cavi_disponibili = [c for c in cavi if c['id_cavo'] not in cavi_certificati]
    
    if len(cavi_disponibili) < 2:
        print("⚠️ Pochi cavi disponibili per il test, procedo comunque...")
        cavi_disponibili = cavi[:2]
    
    strumento_id = strumenti[0]['id_strumento'] if strumenti else None
    
    print(f"\n=== TEST 1: Certificazione Backend Diretta ===")
    if cavi_disponibili:
        cavo_test_1 = cavi_disponibili[0]['id_cavo']
        result1 = test_certificazione_backend(token, cantiere_id, cavo_test_1, strumento_id)
    
    print(f"\n=== TEST 2: Certificazione Frontend Next.js ===")
    if len(cavi_disponibili) > 1:
        cavo_test_2 = cavi_disponibili[1]['id_cavo']
        result2 = test_certificazione_frontend(token, cantiere_id, cavo_test_2, strumento_id)
    
    # Analisi finale
    analizza_database_certificazioni(token, cantiere_id)
    
    print(f"\n🏁 Test completati!")
    print(f"📊 Verifica l'integrità dei dati nel database e la consistenza tra le interfacce")

if __name__ == "__main__":
    main()
