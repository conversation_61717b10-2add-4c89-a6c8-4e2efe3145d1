{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KuIusqDD6OLJzwFfBncNZmvH4WjdfFWZOoEuCU5Iwuw=", "__NEXT_PREVIEW_MODE_ID": "a0e19ed0d0bbd3fc8ee80a2fe47c77dc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d6728a24c7e99768c4ace57a9f30c1d8c52f85e0c6fc39dc95164666077c1e08", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "29ea827af23dd8fbc9417176c8bba298ba0cef64d7a52165eb5f5154c7216aa8"}}}, "sortedMiddleware": ["/"], "functions": {}}