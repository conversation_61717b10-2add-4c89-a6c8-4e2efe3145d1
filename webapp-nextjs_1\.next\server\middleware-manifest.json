{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KuIusqDD6OLJzwFfBncNZmvH4WjdfFWZOoEuCU5Iwuw=", "__NEXT_PREVIEW_MODE_ID": "7f981f7f9c29377bd526f6fd501ae681", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "64733ab1322e3f57044e6aedd7826cb381954cd08a248aa8ff4e4831fab85d12", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d615aea095af649b9c368d32d3c2bc20d40caf38209a3199ae2e2d3a44a520dd"}}}, "sortedMiddleware": ["/"], "functions": {}}