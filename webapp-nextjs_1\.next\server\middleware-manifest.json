{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "KuIusqDD6OLJzwFfBncNZmvH4WjdfFWZOoEuCU5Iwuw=", "__NEXT_PREVIEW_MODE_ID": "d719859748ce6bea47844a82aa9cbe01", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4eb3d71d5446a06c528ef1ade4bbde578ca280d02b496c01e76a7f6c1759ed40", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "85fcf49eddca7c4aff8af0768deb2ab4726b94743c6c9a3fb543f16ffb4c27c2"}}}, "sortedMiddleware": ["/"], "functions": {}}