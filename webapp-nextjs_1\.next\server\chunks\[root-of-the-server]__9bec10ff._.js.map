{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs_1/src/app/api/cavi/%5BcantiereId%5D/%5BcavoId%5D/mark-as-spare/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ cantiereId: string; cavoId: string }> }\n) {\n  try {\n    const { cantiereId, cavoId } = await params\n    \n    // Estrai il token di autorizzazione dall'header\n    const authHeader = request.headers.get('authorization')\n    \n    if (!authHeader || !authHeader.startsWith('Bearer ')) {\n      return NextResponse.json(\n        { \n          detail: 'Token di autorizzazione mancante' \n        }, \n        { status: 401 }\n      )\n    }\n\n    // Leggi il body della richiesta\n    const body = await request.json().catch(() => ({}))\n\n    // Proxy la richiesta al backend FastAPI\n    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n    const url = `${backendUrl}/api/cavi/${cantiereId}/${cavoId}/mark-as-spare`\n    \n    console.log('🔄 Mark-as-Spare API: Proxying POST request to backend:', url)\n    console.log('🔄 Mark-as-Spare API: Request body:', body)\n    \n    const response = await fetch(url, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': authHeader\n      },\n      body: JSON.stringify(body)\n    })\n\n    console.log('📡 Mark-as-Spare API: Backend response status:', response.status)\n\n    if (!response.ok) {\n      const errorData = await response.json().catch(() => ({ detail: 'Errore sconosciuto' }))\n      console.error('❌ Mark-as-Spare API: Backend error:', errorData)\n      return NextResponse.json(errorData, { \n        status: response.status \n      })\n    }\n\n    const data = await response.json()\n    console.log('📡 Mark-as-Spare API: Backend response data:', data)\n\n    return NextResponse.json(data, { \n      status: response.status,\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    })\n  } catch (error) {\n    console.error('❌ Mark-as-Spare API: Errore interno:', error)\n    return NextResponse.json(\n      { detail: 'Errore interno del server' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAA+D;IAEvE,IAAI;QACF,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,MAAM;QAErC,gDAAgD;QAChD,MAAM,aAAa,QAAQ,OAAO,CAAC,GAAG,CAAC;QAEvC,IAAI,CAAC,cAAc,CAAC,WAAW,UAAU,CAAC,YAAY;YACpD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,QAAQ;YACV,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,OAAO,MAAM,QAAQ,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QAEjD,wCAAwC;QACxC,MAAM,aAAa,6DAAmC;QACtD,MAAM,MAAM,GAAG,WAAW,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,cAAc,CAAC;QAE1E,QAAQ,GAAG,CAAC,2DAA2D;QACvE,QAAQ,GAAG,CAAC,uCAAuC;QAEnD,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,QAAQ,GAAG,CAAC,kDAAkD,SAAS,MAAM;QAE7E,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAqB,CAAC;YACrF,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,WAAW;gBAClC,QAAQ,SAAS,MAAM;YACzB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,gDAAgD;QAE5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAC7B,QAAQ,SAAS,MAAM;YACvB,SAAS;gBACP,gBAAgB;YAClB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,QAAQ;QAA4B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}