#!/usr/bin/env python3
"""
Test completo per verificare la funzionalità di collegamento cavi
dopo le correzioni implementate.
"""

import requests
import json

# Configurazione
BASE_URL = "http://localhost:8001"
FRONTEND_URL = "http://localhost:3001"

def login():
    """Effettua il login e restituisce il token."""
    login_data = {
        "username": "admin",
        "password": "admin"
    }
    
    # Il backend richiede form data per il login
    response = requests.post(f"{BASE_URL}/api/auth/login", data=login_data)
    if response.status_code == 200:
        data = response.json()
        return data.get("access_token")
    else:
        print(f"❌ Errore login: {response.status_code} - {response.text}")
        return None

def get_cavi_installati(token, cantiere_id):
    """Recupera i cavi installati."""
    headers = {"Authorization": f"Bearer {token}"}
    response = requests.get(f"{BASE_URL}/api/cavi/{cantiere_id}?stato=Installato", headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"❌ Errore recupero cavi: {response.status_code} - {response.text}")
        return []

def test_collegamento_backend(token, cantiere_id, cavo_id, lato):
    """Testa il collegamento direttamente al backend."""
    headers = {"Authorization": f"Bearer {token}"}
    data = {"lato": lato, "responsabile": "test"}
    
    print(f"🔄 Test collegamento backend: {lato} per cavo {cavo_id}")
    response = requests.post(f"{BASE_URL}/api/cavi/{cantiere_id}/{cavo_id}/collegamento", 
                           json=data, headers=headers)
    
    print(f"📡 Risposta backend: {response.status_code}")
    if response.status_code == 200:
        print(f"✅ Collegamento backend riuscito")
        return response.json()
    else:
        print(f"❌ Errore collegamento backend: {response.text}")
        return None

def test_collegamento_frontend(token, cantiere_id, cavo_id, lato):
    """Testa il collegamento tramite frontend Next.js."""
    headers = {"Authorization": f"Bearer {token}"}
    data = {"lato": lato, "responsabile": "test"}
    
    print(f"🔄 Test collegamento frontend: {lato} per cavo {cavo_id}")
    response = requests.post(f"{FRONTEND_URL}/api/cavi/{cantiere_id}/{cavo_id}/collegamento", 
                           json=data, headers=headers)
    
    print(f"📡 Risposta frontend: {response.status_code}")
    if response.status_code == 200:
        print(f"✅ Collegamento frontend riuscito")
        return response.json()
    else:
        print(f"❌ Errore collegamento frontend: {response.text}")
        try:
            error_data = response.json()
            print(f"📝 Dettagli errore: {error_data}")
        except:
            pass
        return None

def main():
    print("🚀 Avvio test collegamento cavi completo")
    
    # Login
    token = login()
    if not token:
        print("❌ Impossibile effettuare il login")
        return
    
    print("✅ Login riuscito, token ottenuto")
    
    # Recupera cavi installati
    cantiere_id = 1
    cavi = get_cavi_installati(token, cantiere_id)
    
    if not cavi:
        print("❌ Nessun cavo installato trovato")
        return
    
    print(f"📋 Trovati {len(cavi)} cavi installati")
    
    # Trova un cavo installato con stato_installazione = "Installato"
    cavo_test = None
    for cavo in cavi:
        if cavo.get('stato_installazione') == 'Installato' and cavo.get('collegamenti', 0) < 3:
            cavo_test = cavo
            break

    if not cavo_test:
        # Se non ci sono cavi parzialmente collegati, usa il primo installato
        for cavo in cavi:
            if cavo.get('stato_installazione') == 'Installato':
                cavo_test = cavo
                break

    if not cavo_test:
        print("❌ Nessun cavo installato trovato")
        return

    print(f"🎯 Test con cavo installato: {cavo_test['id_cavo']} (stato: {cavo_test.get('stato_installazione')}, collegamenti: {cavo_test.get('collegamenti', 0)})")
    
    cavo_id = cavo_test['id_cavo']
    
    print(f"\n=== TEST 1: Collegamento Partenza (Backend) ===")
    result1 = test_collegamento_backend(token, cantiere_id, cavo_id, "partenza")
    
    print(f"\n=== TEST 2: Collegamento Arrivo (Frontend) ===")
    result2 = test_collegamento_frontend(token, cantiere_id, cavo_id, "arrivo")
    
    print(f"\n=== TEST 3: Collegamento Partenza (Frontend) ===")
    result3 = test_collegamento_frontend(token, cantiere_id, cavo_id, "partenza")
    
    print(f"\n🏁 Test completati!")
    
    # Verifica stato finale
    cavi_finali = get_cavi_installati(token, cantiere_id)
    cavo_finale = next((c for c in cavi_finali if c['id_cavo'] == cavo_id), None)
    if cavo_finale:
        print(f"📊 Stato finale cavo {cavo_id}: collegamenti = {cavo_finale.get('collegamenti', 0)}")

if __name__ == "__main__":
    main()
