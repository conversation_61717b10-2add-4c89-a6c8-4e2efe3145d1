{"/_not-found/page": "app/_not-found/page.js", "/api/auth/test-token/route": "app/api/auth/test-token/route.js", "/api/cantieri/[cantiereId]/statistics/route": "app/api/cantieri/[cantiereId]/statistics/route.js", "/api/cantieri/route": "app/api/cantieri/route.js", "/api/cavi/[cantiereId]/[cavoId]/cancel-installation/route": "app/api/cavi/[cantiereId]/[cavoId]/cancel-installation/route.js", "/api/cavi/[cantiereId]/[cavoId]/metri-posati/route": "app/api/cavi/[cantiereId]/[cavoId]/metri-posati/route.js", "/api/cavi/[cantiereId]/[cavoId]/route": "app/api/cavi/[cantiereId]/[cavoId]/route.js", "/api/cavi/[cantiereId]/route": "app/api/cavi/[cantiereId]/route.js", "/api/parco-cavi/[cantiereId]/[numeroBobina]/route": "app/api/parco-cavi/[cantiereId]/[numeroBobina]/route.js", "/api/parco-cavi/[cantiereId]/route": "app/api/parco-cavi/[cantiereId]/route.js", "/cantieri/page": "app/cantieri/page.js", "/cavi/page": "app/cavi/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js", "/parco-cavi/page": "app/parco-cavi/page.js"}