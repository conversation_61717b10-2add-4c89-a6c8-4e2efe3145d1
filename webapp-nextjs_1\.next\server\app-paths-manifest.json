{"/_not-found/page": "app/_not-found/page.js", "/api/auth/test-token/route": "app/api/auth/test-token/route.js", "/api/cantieri/[cantiereId]/certificazioni/route": "app/api/cantieri/[cantiereId]/certificazioni/route.js", "/api/cantieri/[cantiereId]/route": "app/api/cantieri/[cantiereId]/route.js", "/api/cantieri/[cantiereId]/statistics/route": "app/api/cantieri/[cantiereId]/statistics/route.js", "/api/cantieri/[cantiereId]/strumenti/route": "app/api/cantieri/[cantiereId]/strumenti/route.js", "/api/cantieri/[cantiereId]/weather/route": "app/api/cantieri/[cantiereId]/weather/route.js", "/api/cantieri/route": "app/api/cantieri/route.js", "/api/cavi/[cantiereId]/[cavoId]/collegamento/route": "app/api/cavi/[cantiereId]/[cavoId]/collegamento/route.js", "/api/cavi/[cantiereId]/route": "app/api/cavi/[cantiereId]/route.js", "/api/parco-cavi/[cantiereId]/route": "app/api/parco-cavi/[cantiereId]/route.js", "/api/responsabili/cantiere/[cantiereId]/route": "app/api/responsabili/cantiere/[cantiereId]/route.js", "/cantieri/[id]/page": "app/cantieri/[id]/page.js", "/cantieri/page": "app/cantieri/page.js", "/cavi/page": "app/cavi/page.js", "/page": "app/page.js", "/parco-cavi/page": "app/parco-cavi/page.js"}